# git-cliff configuration file
# https://git-cliff.org/docs/configuration
#
# Lines starting with "#" are comments.
# Configuration options are organized into tables and keys.
# See documentation for more information on available options.

[changelog]
header = """
## What's new
"""
# template for the changelog body
# https://tera.netlify.app/docs
body = """
{% for group, commits in commits | group_by(attribute="group") %}\
    {% for commit in commits %}
        - {{ commit.message | upper_first | split(pat="\n") | first | trim }}\
        {% if commit.remote.username %} by @{{ commit.remote.username }}{%- endif %}\
    {% endfor %}\
{% endfor %}
"""
# remove the leading and trailing whitespace from the template
trim = true
footer = """
"""
postprocessors = []
[git]
# parse the commits based on https://www.conventionalcommits.org
conventional_commits = true
# filter out the commits that are not conventional
filter_unconventional = false
# process each line of a commit as an individual commit
split_commits = false
# regex for preprocessing the commit messages
commit_preprocessors = []
# regex for parsing and grouping commits
commit_parsers = [
    { message = "^build\\(deps\\)", skip = true },
    { message = "^build\\(deps-dev\\)", skip = true },
    { message = "^ci", skip = true },
    { body = ".*", group = "Changes" },
]
# protect breaking changes from being skipped due to matching a skipping commit_parser
protect_breaking_commits = false
# filter out the commits that are not matched by commit parsers
filter_commits = false
# glob pattern for matching git tags
tag_pattern = "v[0-9]*"
# regex for skipping tags
skip_tags = ""
# regex for ignoring tags
ignore_tags = ""
# sort the tags topologically
topo_order = false
# sort the commits inside sections by oldest/newest order
sort_commits = "newest"
# limit the number of commits included in the changelog.
# limit_commits = 42
