[package]
name = "solana-zk-sdk"
description = "Solana ZK SDK"
documentation = "https://docs.rs/solana-zk-sdk"
version = "4.0.0"
authors = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
edition = { workspace = true }

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
base64 = { workspace = true }
bytemuck = { workspace = true }
bytemuck_derive = { workspace = true }
num-derive = { workspace = true }
num-traits = { workspace = true }
solana-instruction = { workspace = true, features = ["std"] }
solana-pubkey = { workspace = true, features = ["bytemuck"] }
solana-sdk-ids = { workspace = true }
thiserror = { workspace = true }

[target.'cfg(not(target_os = "solana"))'.dependencies]
aes-gcm-siv = { workspace = true }
bincode = { workspace = true }
curve25519-dalek = { workspace = true, features = ["serde"] }
itertools = { workspace = true }
merlin = { workspace = true }
rand = { workspace = true }
serde = { workspace = true }
serde_derive = { workspace = true }
serde_json = { workspace = true }
sha3 = { workspace = true }
solana-derivation-path = { workspace = true }
solana-seed-derivable = { workspace = true }
solana-seed-phrase = { workspace = true }
solana-signature = { workspace = true }
solana-signer = { workspace = true }
subtle = { workspace = true }
zeroize = { workspace = true, features = ["zeroize_derive"] }

[target.'cfg(target_arch = "wasm32")'.dependencies]
getrandom = { workspace = true, features = ["js"] }
js-sys = { workspace = true }
wasm-bindgen = { workspace = true }

[dev-dependencies]
solana-keypair = { workspace = true }
tiny-bip39 = { workspace = true }

[lints]
workspace = true
