//! Errors related to proving and verifying proofs.
use thiserror::<PERSON><PERSON><PERSON>;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum AuthenticatedEncryptionError {
    #[error("key derivation method not supported")]
    DerivationMethodNotSupported,
    #[error("seed length too short for derivation")]
    SeedLengthTooShort,
    #[error("seed length too long for derivation")]
    SeedLengthTooLong,
    #[error("failed to deserialize")]
    Deserialization,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Debug, Eq, PartialEq)]
pub enum ElGamalError {
    #[error("key derivation method not supported")]
    DerivationMethodNotSupported,
    #[error("seed length too short for derivation")]
    SeedLengthTooShort,
    #[error("seed length too long for derivation")]
    SeedLengthTooLong,
    #[error("failed to deserialize ciphertext")]
    CiphertextDeserialization,
    #[error("failed to deserialize public key")]
    PubkeyDeserialization,
    #[error("failed to deserialize keypair")]
    KeypairDeserialization,
    #[error("failed to deserialize secret key")]
    SecretKeyDeserialization,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>q, PartialEq)]
pub enum TranscriptError {
    #[error("point is the identity")]
    ValidationError,
}

#[derive(Error, Debug, Clone, Eq, PartialEq)]
pub enum ParseError {
    #[error("String is the wrong size")]
    WrongSize,
    #[error("Invalid Base64 string")]
    Invalid,
}
