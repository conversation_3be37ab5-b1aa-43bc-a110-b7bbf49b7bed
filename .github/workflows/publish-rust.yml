name: Publish Rust Crate

on:
  workflow_dispatch:
    inputs:
      package_path:
        description: Path to directory with package to release
        required: true
        default: "zk-sdk"
        type: choice
        options:
          - zk-sdk
      level:
        description: Level
        required: true
        default: patch
        type: choice
        options:
          - patch
          - minor
          - major
          - rc
          - beta
          - alpha
          - release
          - version
      version:
        description: Version (used with level "version")
        required: false
        type: string
      dry_run:
        description: Dry run
        required: true
        default: true
        type: boolean
      create_release:
        description: Create a GitHub release
        required: true
        type: boolean
        default: true

jobs:
  test:
    name: Test Rust Crate
    runs-on: ubuntu-latest
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      - name: Setup Environment
        uses: ./.github/actions/setup
        with:
          clippy: true
          rustfmt: true
          solana: true
          wasm: true

      - name: Format
        run: pnpm zx ./scripts/rust/format.mjs "${{ inputs.package_path }}"

      - name: Lint
        run: pnpm zx ./scripts/rust/lint.mjs "${{ inputs.package_path }}"

      - name: Build SBF
        run: pnpm zx ./scripts/rust/build-sbf.mjs "${{ inputs.package_path }}"

      - name: Build WASM
        run: pnpm zx ./scripts/rust/build-wasm.mjs "${{ inputs.package_path }}"

      - name: Test
        run: pnpm zx ./scripts/rust/test.mjs "${{ inputs.package_path }}"

  semver:
    name: Check Semver
    runs-on: ubuntu-latest
    steps:
      - name: Git checkout
        uses: actions/checkout@v4

      - name: Setup Environment
        uses: ./.github/actions/setup
        with:
          cli: true
          cargo-cache-key: cargo-publish-semver-${{ inputs.package_path }}
          cargo-cache-fallback-key: cargo-publish-semver

      - name: Install cargo-semver-checks
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-semver-checks,cargo-release

      - name: Set Git Author (required for cargo-release)
        run: |
          git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

      - name: Set Version
        run: |
          if [ "${{ inputs.level }}" == "version" ]; then
            LEVEL=${{ inputs.version }}
          else
            LEVEL=${{ inputs.level }}
          fi
          cargo release $LEVEL --manifest-path "${{ inputs.package_path }}/Cargo.toml" --no-tag --no-publish --no-push --no-confirm --execute

      - name: Check semver
        run: pnpm rust:semver --manifest-path "${{ inputs.package_path }}/Cargo.toml"

  publish:
    name: Publish Rust Crate
    runs-on: ubuntu-latest
    needs: [test, semver]
    permissions:
      contents: write
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.ANZA_TEAM_PAT }}
          fetch-depth: 0 # get the whole history for git-cliff

      - name: Setup Environment
        uses: ./.github/actions/setup
        with:
          cli: true
          cargo-cache-key: cargo-publish-${{ inputs.package_path }}
          cargo-cache-fallback-key: cargo-publish

      - name: Install cargo-release
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-release

      - name: Ensure CARGO_REGISTRY_TOKEN variable is set
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
        if: ${{ env.CARGO_REGISTRY_TOKEN == '' }}
        run: |
          echo "The CARGO_REGISTRY_TOKEN secret variable is not set"
          echo "Go to \"Settings\" -> \"Secrets and variables\" -> \"Actions\" -> \"New repository secret\"."
          exit 1

      - name: Set Git Author
        run: |
          git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

      - name: Publish Crate
        id: publish
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
        run: |
          if [ "${{ inputs.level }}" == "version" ]; then
            LEVEL=${{ inputs.version }}
          else
            LEVEL=${{ inputs.level }}
          fi

          if [ "${{ inputs.dry_run }}" == "true" ]; then
            OPTIONS="--dry-run"
          else
            OPTIONS=""
          fi

          pnpm rust:publish "${{ inputs.package_path }}" $LEVEL $OPTIONS

      - name: Generate a changelog
        if: github.event.inputs.create_release == 'true'
        uses: orhun/git-cliff-action@v4
        with:
          config: "scripts/cliff.toml"
          args: ${{ steps.publish.outputs.old_git_tag }}..HEAD --include-path "${{ inputs.package_path }}/**" --github-repo ${{ github.repository }}
        env:
          OUTPUT: TEMP_CHANGELOG.md
          GITHUB_REPO: ${{ github.repository }}

      - name: Create GitHub release
        if: github.event.inputs.create_release == 'true' && github.event.inputs.dry_run != 'true'
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ steps.publish.outputs.new_git_tag }}
          bodyFile: TEMP_CHANGELOG.md
