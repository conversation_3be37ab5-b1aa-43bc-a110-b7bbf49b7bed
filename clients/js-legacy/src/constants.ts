import { PublicKey } from '@solana/web3.js';

export const ZK_ELGAMAL_PROOF_PROGRAM_ID = new PublicKey('ZkE1Gama1Proof11111111111111111111111111111');

export const CONTEXT_STATE_META_SIZE = 33;
export const ZERO_CIPHERTEXT_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 96;
export const CIPHERTEXT_CIPHERTEXT_EQUALITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 192;
export const CIPHERTEXT_COMMITMENT_EQUALITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 128;
export const PUBKEY_VALIDITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 32;
export const PERCENTAGE_WITH_CAP_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 104;
export const BATCHED_RANGE_PROOF_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 264;
export const GROUPED_CIPHERTEXT_2_HANDLES_VALIDITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 160;
export const BATCHED_GROUPED_CIPHERTEXT_2_HANDLES_VALIDITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 256;
export const GROUPED_CIPHERTEXT_3_HANDLES_VALIDITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 224;
export const BATCHED_GROUPED_CIPHERTEXT_3_HANDLES_VALIDITY_CONTEXT_ACCOUNT_SIZE = CONTEXT_STATE_META_SIZE + 352;
