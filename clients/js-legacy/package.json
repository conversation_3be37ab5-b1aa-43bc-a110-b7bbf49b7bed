{"name": "@solana/zk-elgamal-proof", "description": "JavaScript client for the ZK ElGamal Proof program", "version": "0.1.0", "repository": "https://github.com/solana-program/zk-elgamal-proof", "license": "Apache-2.0", "type": "module", "sideEffects": false, "engines": {"node": ">=16"}, "files": ["lib", "src", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"types": "./lib/types/index.d.ts", "require": "./lib/cjs/index.js", "import": "./lib/esm/index.js"}, "scripts": {"prepublishOnly": "pnpm build", "build": "tsc --build --verbose tsconfig.all.json", "postbuild": "shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json", "lint": "eslint --max-warnings 0 .", "lint:fix": "eslint --fix .", "format": "prettier --check src test", "format:fix": "prettier --write src test", "test": "mocha test/* && pnpm test:exports", "test:exports": "node ./test/exports/module.mjs && node ./test/exports/commonjs.cjs"}, "dependencies": {"@solana/codecs-numbers": "^2.0.0", "@solana/web3.js": "^1.95.5", "@solana/zk-sdk": "0.1.2"}, "devDependencies": {"@solana/prettier-config-solana": "0.0.5", "@solana/spl-record": "^0.1.0", "@eslint/js": "^9.19.0", "@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "@types/node": "^22.10.1", "chai": "^5.2.1", "chai-as-promised": "^8.0.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-require-extensions": "^0.1.1", "globals": "^15.14.0", "mocha": "^11.0.1", "prettier": "^3.4.2", "shx": "^0.3.4", "start-server-and-test": "^2.0.12", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.22.0"}, "prettier": "@solana/prettier-config-solana", "packageManager": "pnpm@9.1.0"}