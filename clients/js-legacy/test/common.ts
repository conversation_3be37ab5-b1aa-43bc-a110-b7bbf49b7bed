import type { Signer } from '@solana/web3.js';
import { <PERSON><PERSON><PERSON>, Keypair, Connection } from '@solana/web3.js';
import { ZK_ELGAMAL_PROOF_PROGRAM_ID } from '../src';

export async function newAccountWithLamports(connection: Connection, lamports = 1000000): Promise<Signer> {
    const account = Keypair.generate();
    const signature = await connection.requestAirdrop(account.publicKey, lamports);
    await connection.confirmTransaction(signature);
    return account;
}

export async function getConnection(): Promise<Connection> {
    const url = 'http://127.0.0.1:8899';
    const connection = new Connection(url, 'confirmed');
    return connection;
}

export const TEST_PROGRAM_ID = process.env.TEST_PROGRAM_ID
    ? new PublicKey(process.env.TEST_PROGRAM_ID)
    : ZK_ELGAMAL_PROOF_PROGRAM_ID;
