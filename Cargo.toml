[workspace]
resolver = "2"
members = ["zk-sdk"]

[workspace.package]
authors = ["Anza Maintainers <<EMAIL>>"]
repository = "https://github.com/solana-program/zk-elgamal-proof"
homepage = "https://anza.xyz/"
license = "Apache-2.0"
edition = "2021"

[workspace.lints.rust]
warnings = "deny"

[workspace.lints.rust.unexpected_cfgs]
level = "warn"
check-cfg = ['cfg(target_os, values("solana"))']

[workspace.metadata.cli]
solana = "2.2.11"

# Specify Rust toolchains for rustfmt, clippy, and build.
# Any unprovided toolchains default to stable.
[workspace.metadata.toolchains]
format = "nightly-2025-03-29"
lint = "nightly-2025-03-29"

[workspace.dependencies]
aes-gcm-siv = "0.11.1"
base64 = "0.22.1"
bincode = "1.3.3"
bytemuck = "1.23.2"
bytemuck_derive = "1.9.3"
curve25519-dalek = { version = "4.1.3", features = ["digest", "rand_core"] }
getrandom = "0.2"
itertools = "0.12.1"
js-sys = "0.3.77"
merlin = { version = "3", default-features = false }
num-derive = "0.4"
num-traits = "0.2"
rand = "0.8.5"
serde = "1.0.219"
serde_derive = "1.0.219"
serde_json = "1.0.142"
sha3 = "0.10.8"
solana-derivation-path = "3.0.0"
solana-instruction = "3.0.0"
solana-keypair = "3.0.0"
solana-pubkey = { version = "3.0.0", default-features = false }
solana-sdk-ids = "3.0.0"
solana-seed-derivable = "3.0.0"
solana-seed-phrase = "3.0.0"
solana-signature = { version = "3.0.0", default-features = false }
solana-signer = "3.0.0"
subtle = "2.6.1"
thiserror = "2.0.14"
tiny-bip39 = "0.8.2"
wasm-bindgen = "0.2"
zeroize = { version = "1.7", default-features = false }

[profile.release]
split-debuginfo = "unpacked"
lto = "thin"

# Enable basic optimizations for unittests
[profile.test]
opt-level = 1

# Enable optimizations for procmacros for faster recompile
[profile.dev.build-override]
opt-level = 1

# curve25519-dalek uses the simd backend by default in v4 if possible,
# which has very slow performance on some platforms with opt-level 0,
# which is the default for dev and test builds.
# This slowdown causes certain interactions in the solana-test-validator,
# such as verifying ZK proofs in transactions, to take much more than 400ms,
# creating problems in the testing environment.
# To enable better performance in solana-test-validator during tests and dev builds,
# we override the opt-level to 3 for the crate.
[profile.dev.package.curve25519-dalek]
opt-level = 3
